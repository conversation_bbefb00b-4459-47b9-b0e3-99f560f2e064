<?php
// Database configuration for Dar Alhuriya contact messages
// Using SQLite for simplicity - no additional server setup required

// Database file path
$db_file = 'messages.db';

// Initialize database connection
function getDatabase() {
    global $db_file;
    
    try {
        $pdo = new PDO('sqlite:' . $db_file);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create table if it doesn't exist
        createMessagesTable($pdo);
        
        return $pdo;
    } catch (PDOException $e) {
        throw new Exception('Database connection failed: ' . $e->getMessage());
    }
}

// Create messages table
function createMessagesTable($pdo) {
    $sql = "CREATE TABLE IF NOT EXISTS messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT NOT NULL,
        phone TEXT,
        message TEXT NOT NULL,
        status TEXT DEFAULT 'received',
        ip_address TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($sql);
}

// Store a new message
function storeMessage($name, $email, $phone, $message) {
    $pdo = getDatabase();
    
    $sql = "INSERT INTO messages (name, email, phone, message, ip_address) 
            VALUES (:name, :email, :phone, :message, :ip_address)";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        ':name' => $name,
        ':email' => $email,
        ':phone' => $phone,
        ':message' => $message,
        ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ]);
    
    return $pdo->lastInsertId();
}

// Update message status
function updateMessageStatus($message_id, $status) {
    $pdo = getDatabase();
    
    $sql = "UPDATE messages SET status = :status, updated_at = CURRENT_TIMESTAMP 
            WHERE id = :id";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        ':status' => $status,
        ':id' => $message_id
    ]);
}

// Get all messages with pagination
function getMessages($page = 1, $per_page = 20, $status = null) {
    $pdo = getDatabase();
    
    $offset = ($page - 1) * $per_page;
    
    $where_clause = '';
    $params = [];
    
    if ($status) {
        $where_clause = 'WHERE status = :status';
        $params[':status'] = $status;
    }
    
    $sql = "SELECT * FROM messages $where_clause 
            ORDER BY created_at DESC 
            LIMIT :limit OFFSET :offset";
    
    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':limit', $per_page, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Get total message count
function getMessageCount($status = null) {
    $pdo = getDatabase();
    
    $where_clause = '';
    $params = [];
    
    if ($status) {
        $where_clause = 'WHERE status = :status';
        $params[':status'] = $status;
    }
    
    $sql = "SELECT COUNT(*) as count FROM messages $where_clause";
    
    $stmt = $pdo->prepare($sql);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return $result['count'];
}

// Get a single message by ID
function getMessage($id) {
    $pdo = getDatabase();
    
    $sql = "SELECT * FROM messages WHERE id = :id";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([':id' => $id]);
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// Mark message as read
function markMessageAsRead($id) {
    updateMessageStatus($id, 'read');
}

// Delete a message
function deleteMessage($id) {
    $pdo = getDatabase();
    
    $sql = "DELETE FROM messages WHERE id = :id";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([':id' => $id]);
    
    return $stmt->rowCount() > 0;
}

// Get message statistics
function getMessageStats() {
    $pdo = getDatabase();
    
    $sql = "SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN status = 'received' THEN 1 END) as unread,
                COUNT(CASE WHEN status = 'read' THEN 1 END) as read,
                COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent,
                COUNT(CASE WHEN status = 'email_failed' THEN 1 END) as email_failed
            FROM messages";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}
?>
