<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EmailJS Setup - Dar <PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .step {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007cba;
        }
        .code {
            background: #2d2d2d;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <h1>EmailJS Setup for Dar Alhuriya Contact Form</h1>
    
    <div class="warning">
        <strong>Why EmailJS?</strong> Your server's PHP mail() function might not be working properly. EmailJS is a reliable alternative that works without server configuration.
    </div>

    <div class="step">
        <h2>Step 1: Create EmailJS Account</h2>
        <p>1. Go to <a href="https://www.emailjs.com/" target="_blank">https://www.emailjs.com/</a></p>
        <p>2. Click "Sign Up" and create a free account</p>
        <p>3. Verify your email address</p>
    </div>

    <div class="step">
        <h2>Step 2: Add Email Service</h2>
        <p>1. In your EmailJS dashboard, go to "Email Services"</p>
        <p>2. Click "Add New Service"</p>
        <p>3. Choose "Gmail" (recommended)</p>
        <p>4. Connect your Gmail account (<EMAIL>)</p>
        <p>5. Note down your <strong>Service ID</strong></p>
    </div>

    <div class="step">
        <h2>Step 3: Create Email Template</h2>
        <p>1. Go to "Email Templates" in your dashboard</p>
        <p>2. Click "Create New Template"</p>
        <p>3. Use this template content:</p>
        
        <div class="code">
Subject: Dar Alhuriya Contact Form - {{from_name}}

New contact form submission:

Name: {{from_name}}
Email: {{from_email}}
Phone: {{phone}}
Message:
{{message}}

---
Sent from Dar Alhuriya website
Date: {{date}}
        </div>
        
        <p>4. Set the "To Email" to: <EMAIL></p>
        <p>5. Save and note down your <strong>Template ID</strong></p>
    </div>

    <div class="step">
        <h2>Step 4: Get Your Public Key</h2>
        <p>1. Go to "Account" → "General"</p>
        <p>2. Find your <strong>Public Key</strong></p>
        <p>3. Copy it for the next step</p>
    </div>

    <div class="step">
        <h2>Step 5: Configure Your Website</h2>
        <p>Enter your EmailJS credentials below and click "Generate Code":</p>
        
        <form id="configForm">
            <p>
                <label>Service ID:</label><br>
                <input type="text" id="serviceId" placeholder="service_xxxxxxx" style="width: 300px; padding: 8px;">
            </p>
            <p>
                <label>Template ID:</label><br>
                <input type="text" id="templateId" placeholder="template_xxxxxxx" style="width: 300px; padding: 8px;">
            </p>
            <p>
                <label>Public Key:</label><br>
                <input type="text" id="publicKey" placeholder="xxxxxxxxxxxxxxxx" style="width: 300px; padding: 8px;">
            </p>
            <button type="button" onclick="generateConfig()">Generate Configuration</button>
        </form>
        
        <div id="generatedConfig" style="display: none;">
            <h3>Generated Configuration:</h3>
            <p>Copy this code and replace the content in your <code>js/emailjs-config.js</code> file:</p>
            <div class="code" id="configCode"></div>
            
            <h3>Enable EmailJS in your website:</h3>
            <p>1. Open your <code>index.html</code> file</p>
            <p>2. Find this line near the bottom:</p>
            <div class="code">&lt;!-- &lt;script src="js/emailjs-config.js"&gt;&lt;/script&gt; --&gt;</div>
            <p>3. Remove the comment tags to make it:</p>
            <div class="code">&lt;script src="js/emailjs-config.js"&gt;&lt;/script&gt;</div>
        </div>
    </div>

    <div class="step">
        <h2>Step 6: Test Your Setup</h2>
        <p>1. Upload all files to your server</p>
        <p>2. Test the contact form on your website</p>
        <p>3. Check your email (<EMAIL>)</p>
        <p>4. Check EmailJS dashboard for delivery status</p>
    </div>

    <div class="success">
        <strong>Benefits of EmailJS:</strong>
        <ul>
            <li>✓ Works without server email configuration</li>
            <li>✓ Reliable delivery</li>
            <li>✓ Free tier includes 200 emails/month</li>
            <li>✓ Real-time delivery tracking</li>
            <li>✓ No spam folder issues</li>
        </ul>
    </div>

    <script>
        function generateConfig() {
            const serviceId = document.getElementById('serviceId').value;
            const templateId = document.getElementById('templateId').value;
            const publicKey = document.getElementById('publicKey').value;
            
            if (!serviceId || !templateId || !publicKey) {
                alert('Please fill in all fields');
                return;
            }
            
            const config = `// EmailJS Configuration for Dar Alhuriya Contact Form
const EMAILJS_CONFIG = {
    SERVICE_ID: '${serviceId}',
    TEMPLATE_ID: '${templateId}',
    PUBLIC_KEY: '${publicKey}'
};

// Initialize EmailJS
function initEmailJS() {
    if (typeof emailjs === 'undefined') {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js';
        script.onload = function() {
            emailjs.init(EMAILJS_CONFIG.PUBLIC_KEY);
        };
        document.head.appendChild(script);
    } else {
        emailjs.init(EMAILJS_CONFIG.PUBLIC_KEY);
    }
}

// Send email using EmailJS
function sendEmailViaEmailJS(formData) {
    return new Promise((resolve, reject) => {
        const templateParams = {
            to_email: '<EMAIL>',
            from_name: formData.get('name'),
            from_email: formData.get('email'),
            phone: formData.get('phone') || 'Not provided',
            message: formData.get('message'),
            date: new Date().toLocaleString(),
            reply_to: formData.get('email')
        };

        emailjs.send(
            EMAILJS_CONFIG.SERVICE_ID,
            EMAILJS_CONFIG.TEMPLATE_ID,
            templateParams
        ).then(
            function(response) {
                console.log('Email sent successfully:', response);
                resolve({ success: true, message: 'Email sent successfully' });
            },
            function(error) {
                console.error('Email sending failed:', error);
                reject({ success: false, message: 'Failed to send email' });
            }
        );
    });
}

// Replace the contact form handler
document.addEventListener('DOMContentLoaded', function() {
    initEmailJS();
    
    const contactForm = document.getElementById('propertyForm');
    if (contactForm) {
        // Remove existing event listeners
        const newForm = contactForm.cloneNode(true);
        contactForm.parentNode.replaceChild(newForm, contactForm);
        
        newForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const phone = document.getElementById('phone').value;
            const message = document.getElementById('message').value;

            if (!name || !email || !message) {
                alert('Please fill in all required fields');
                return;
            }

            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
            if (!emailRegex.test(email)) {
                alert('Please enter a valid email address');
                return;
            }

            const submitBtn = newForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Sending...';
            submitBtn.disabled = true;

            const formData = new FormData();
            formData.append('name', name);
            formData.append('email', email);
            formData.append('phone', phone);
            formData.append('message', message);

            sendEmailViaEmailJS(formData)
                .then(data => {
                    alert('Thank you for your message! We will contact you soon.');
                    newForm.reset();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Sorry, there was an error sending your message. Please try again or contact us directly.');
                })
                .finally(() => {
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                });
        });
    }
});`;
            
            document.getElementById('configCode').textContent = config;
            document.getElementById('generatedConfig').style.display = 'block';
        }
    </script>
</body>
</html>
