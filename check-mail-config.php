<?php
// Check PHP mail configuration
echo "<h1>PHP Mail Configuration Check</h1>";

// Check if mail function exists
if (function_exists('mail')) {
    echo "<p style='color: green;'>✓ PHP mail() function is available</p>";
} else {
    echo "<p style='color: red;'>✗ PHP mail() function is NOT available</p>";
    echo "<p><strong>Action needed:</strong> Contact your hosting provider to enable PHP mail() function</p>";
}

// Check PHP configuration
echo "<h2>PHP Configuration</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Server:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";
echo "<p><strong>Server Software:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</p>";

// Check mail-related PHP settings
echo "<h2>Mail-related PHP Settings</h2>";
$mail_settings = [
    'sendmail_path' => ini_get('sendmail_path'),
    'SMTP' => ini_get('SMTP'),
    'smtp_port' => ini_get('smtp_port'),
    'sendmail_from' => ini_get('sendmail_from')
];

foreach ($mail_settings as $setting => $value) {
    $display_value = $value ? $value : 'Not set';
    echo "<p><strong>$setting:</strong> $display_value</p>";
}

// Test basic email sending
echo "<h2>Email Test</h2>";
$test_email = "<EMAIL>";
$subject = "Test from " . $_SERVER['HTTP_HOST'] . " - " . date('Y-m-d H:i:s');
$message = "This is a test email from your hosting server.\n\n";
$message .= "Server: " . $_SERVER['HTTP_HOST'] . "\n";
$message .= "Date: " . date('Y-m-d H:i:s') . "\n";
$message .= "PHP Version: " . phpversion() . "\n";

// Try different From addresses
$from_addresses = [
    "noreply@" . $_SERVER['HTTP_HOST'],
    "contact@" . $_SERVER['HTTP_HOST'],
    "admin@" . $_SERVER['HTTP_HOST']
];

foreach ($from_addresses as $from) {
    echo "<h3>Testing with From: $from</h3>";
    
    $headers = "From: $from\r\n";
    $headers .= "Reply-To: $from\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";
    $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
    
    $result = mail($test_email, $subject . " (From: $from)", $message, $headers);
    
    if ($result) {
        echo "<p style='color: green;'>✓ Email sent successfully with $from</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to send email with $from</p>";
    }
}

echo "<hr>";
echo "<h2>What to Ask Your Hosting Provider</h2>";
echo "<ul>";
echo "<li>Is PHP mail() function enabled?</li>";
echo "<li>Is there an SMTP server configured?</li>";
echo "<li>Do I need to authenticate with SMTP?</li>";
echo "<li>What should I use as the 'From' email address?</li>";
echo "<li>Are there any restrictions on sending emails?</li>";
echo "<li>Do you provide SMTP credentials?</li>";
echo "</ul>";

echo "<hr>";
echo "<p><em>Delete this file after checking for security reasons.</em></p>";
?>
