/* Base Styles */
:root {
    --primary-color: #5d4037; /* <PERSON> brown */
    --secondary-color: #d4a76a; /* Gold/beige */
    --accent-color: #8d6e63; /* Lighter brown */
    --light-color: #f5f5f5; /* Off-white */
    --dark-color: #3e2723; /* Dark brown */
    --text-color: #3e2723; /* Dark brown for text */
    --text-light: #8d6e63; /* Lighter brown for secondary text */
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
}

body.rtl {
    direction: rtl;
    font-family: '<PERSON><PERSON>wal', sans-serif;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

a {
    text-decoration: none;
    color: var(--primary-color);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.btn {
    display: inline-block;
    background-color: var(--secondary-color);
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn:hover {
    background-color: #b58a50; /* Darker gold */
    transform: translateY(-2px);
}

.section-title {
    text-align: center;
    margin-bottom: 40px;
    font-size: 2.5rem;
    color: var(--primary-color);
    position: relative;
    padding-bottom: 15px;
    font-weight: 600;
    letter-spacing: 1px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background-color: var(--secondary-color);
}

.section-title::before {
    content: '';
    position: absolute;
    bottom: 7px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background-color: var(--secondary-color);
}

/* Language Display */
.en, .fr, .ar {
    display: none;
}

body.lang-en .en,
body.lang-fr .fr,
body.lang-ar .ar {
    display: block;
}

/* Navigation menu language display */
.nav-item {
    display: none;
}

body.lang-en .nav-item a.en,
body.lang-fr .nav-item a.fr,
body.lang-ar .nav-item a.ar {
    display: block;
}

body.lang-en .nav-item:has(a.en),
body.lang-fr .nav-item:has(a.fr),
body.lang-ar .nav-item:has(a.ar) {
    display: block;
}

/* For browsers that don't support :has */
@supports not (selector(:has(*))) {
    body.lang-en .nav-item:nth-child(-n+7),
    body.lang-fr .nav-item:nth-child(n+8):nth-child(-n+14),
    body.lang-ar .nav-item:nth-child(n+15) {
        display: block;
    }
}

/* Button language display */
button.en, button.fr, button.ar {
    display: none;
}

body.lang-en button.en,
body.lang-fr button.fr,
body.lang-ar button.ar {
    display: inline-block;
}

/* Inline elements language display */
span.en, span.fr, span.ar,
a.en, a.fr, a.ar,
label.en, label.fr, label.ar {
    display: none;
}

body.lang-en span.en,
body.lang-fr span.fr,
body.lang-ar span.ar,
body.lang-en a.en,
body.lang-fr a.fr,
body.lang-ar a.ar,
body.lang-en label.en,
body.lang-fr label.fr,
body.lang-ar label.ar {
    display: inline-block;
}

/* Header */
header {
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    border-bottom: 3px solid var(--secondary-color);
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 15px;
}

.logo-link {
    text-decoration: none;
    display: block;
    transition: transform 0.3s ease;
    cursor: pointer;
}

.logo-link:hover {
    transform: scale(1.03);
}

.logo h1 {
    font-size: 1.8rem;
    color: var(--primary-color);
    margin-bottom: 0;
}

.logo span {
    color: var(--secondary-color);
}

.logo .tagline {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-top: 0;
    font-style: italic;
}

.nav-links {
    display: flex;
}

.nav-links li {
    margin-left: 30px;
}

.nav-links a {
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover,
.nav-links a.active {
    color: var(--secondary-color);
}

.language-selector {
    display: flex;
    gap: 10px;
}

.lang-btn {
    background: none;
    border: 1px solid var(--secondary-color);
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--primary-color);
}

.lang-btn:hover {
    background-color: rgba(212, 167, 106, 0.2);
}

.lang-btn.active {
    background-color: var(--secondary-color);
    color: white;
    border-color: var(--secondary-color);
}

.mobile-menu-btn {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--primary-color);
    transition: color 0.3s ease;
    padding: 5px;
    border-radius: 4px;
}

.mobile-menu-btn:hover {
    color: var(--secondary-color);
}

/* Hero Section */
.hero {
    height: 100vh;
    background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('../images/492690586_694431343270531_4906913368924556527_n.jpg');
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    padding-top: 80px;
}

.hero-content {
    max-width: 800px;
    padding: 0 20px;
}

.hero h1 {
    font-size: 3.5rem;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 30px;
}

.social-hero {
    margin-top: 20px;
}

.social-hero-link {
    display: inline-flex;
    align-items: center;
    color: white;
    background-color: rgba(66, 103, 178, 0.8);
    padding: 8px 15px;
    border-radius: 30px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.social-hero-link:hover {
    background-color: #4267B2;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.social-hero-link i {
    margin-right: 8px;
    font-size: 1.1rem;
}

/* Gallery Section */
.gallery {
    padding: 100px 0;
    background-color: var(--light-color);
    border-top: 5px solid var(--secondary-color);
}

.gallery-intro {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
    color: var(--text-color);
    line-height: 1.8;
    font-size: 1.1rem;
}

.gallery-container {
    position: relative;
}

.main-image {
    width: 100%;
    height: 500px;
    overflow: hidden;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    border: 3px solid var(--secondary-color);
    position: relative;
}

.image-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 15px;
    text-align: center;
    font-size: 1.1rem;
}

.image-caption p {
    margin: 0;
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.thumbnails {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    overflow-x: auto;
    padding-bottom: 20px;
    justify-content: center;
    max-height: 180px;
    overflow-y: auto;
}

.thumbnails img {
    width: 120px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.thumbnails img:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.thumbnails img.active {
    border: 3px solid var(--secondary-color);
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.gallery-controls {
    position: absolute;
    top: 50%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    transform: translateY(-50%);
}

.gallery-controls button {
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.gallery-controls button:hover {
    background-color: rgba(0, 0, 0, 0.8);
}

/* Property Description */
.property-description {
    padding: 100px 0;
}

.description-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.property-details {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    margin-bottom: 30px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background-color: var(--light-color);
    border-radius: 8px;
    min-width: 120px;
    border: 1px solid var(--secondary-color);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.detail-item i {
    font-size: 2rem;
    color: var(--secondary-color);
    margin-bottom: 10px;
}

.description-text {
    line-height: 1.8;
}

/* Features Section */
.features {
    padding: 100px 0;
    background-color: var(--light-color);
    border-top: 5px solid var(--secondary-color);
}

.features-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    justify-content: center;
}

.feature-item {
    flex: 1;
    min-width: 300px;
    max-width: 400px;
    display: flex;
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    background-color: var(--secondary-color);
    color: white;
    font-size: 2rem;
    padding: 20px;
}

.feature-content {
    padding: 20px;
    flex: 1;
}

.feature-content h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1.3rem;
}

.feature-content p {
    color: var(--text-light);
    font-size: 0.95rem;
    line-height: 1.6;
}

/* Location Section */
.location {
    padding: 100px 0;
    background-color: white;
    border-top: 5px solid var(--secondary-color);
}

/* Reviews Section */
.reviews {
    padding: 100px 0;
    background-color: var(--light-color);
    border-top: 5px solid var(--secondary-color);
}

.reviews-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    justify-content: center;
    margin-bottom: 40px;
}

.review-item {
    flex: 1;
    min-width: 300px;
    max-width: 400px;
    background-color: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.review-item:hover {
    transform: translateY(-5px);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.reviewer-name {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.review-rating {
    color: #FFD700;
}

.review-content p {
    color: var(--text-light);
    line-height: 1.7;
}

.reviews-footer {
    text-align: center;
}

.map-container {
    height: 450px;
    margin-bottom: 30px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    border: 3px solid var(--secondary-color);
}

#map {
    height: 100%;
    width: 100%;
}

.location-details {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: rgba(212, 167, 106, 0.1);
    border-radius: 8px;
    border-left: 4px solid var(--secondary-color);
    line-height: 1.8;
}

/* Contact Section */
.contact {
    padding: 100px 0;
}

.contact-container {
    display: flex;
    flex-wrap: wrap;
    gap: 50px;
}

.contact-form {
    flex: 1;
    min-width: 300px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--accent-color);
    border-radius: 4px;
    font-family: inherit;
    background-color: var(--light-color);
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 5px rgba(212, 167, 106, 0.5);
}

.contact-info {
    flex: 1;
    min-width: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.info-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.info-item i {
    font-size: 1.5rem;
    color: var(--secondary-color);
    margin-right: 15px;
    width: 30px;
}

.map-link, .whatsapp-link, .phone-link, .email-link, .facebook-link {
    color: var(--secondary-color);
    text-decoration: underline;
    transition: color 0.3s ease;
}

.map-link:hover, .whatsapp-link:hover, .phone-link:hover, .email-link:hover, .facebook-link:hover {
    color: var(--primary-color);
}

.whatsapp-link {
    color: #25D366;
}

.whatsapp-link:hover {
    color: #128C7E;
}

.facebook-link {
    color: #4267B2;
}

.facebook-link:hover {
    color: #3b5998;
}

.phone-link {
    color: var(--secondary-color);
}

.phone-link:hover {
    color: var(--primary-color);
}

/* Footer */
footer {
    background-color: var(--dark-color);
    color: white;
    text-align: center;
    padding: 30px 0;
    border-top: 5px solid var(--secondary-color);
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: white;
    color: var(--dark-color);
    border-radius: 50%;
    transition: all 0.3s ease;
    font-size: 1.2rem;
}

.social-icon:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.social-icon:nth-child(1) {
    background-color: #4267B2;
    color: white;
}

.social-icon:nth-child(2) {
    background-color: #25D366;
    color: white;
}

.social-icon:nth-child(3) {
    background-color: #DB4437;
    color: white;
}

/* Scroll to Top Button */
#scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    z-index: 1000;
}

#scroll-to-top.visible {
    opacity: 1;
    visibility: visible;
}

#scroll-to-top:hover {
    background-color: var(--primary-color);
    transform: translateY(-5px);
}



/* Responsive Design */
@media (max-width: 992px) {
    .hero h1 {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .nav-links {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: white;
        flex-direction: column;
        padding: 20px 0;
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
        z-index: 999;
        border-top: 2px solid var(--secondary-color);
        border-bottom: 2px solid var(--secondary-color);
    }

    .nav-links.active {
        display: flex;
    }

    .nav-links li {
        margin: 10px 0;
        text-align: center;
        width: 100%;
    }

    /* Adjust mobile menu for language-specific display */
    body.lang-en .nav-item:has(a.en),
    body.lang-fr .nav-item:has(a.fr),
    body.lang-ar .nav-item:has(a.ar) {
        display: block;
    }

    /* For browsers that don't support :has */
    @supports not (selector(:has(*))) {
        body.lang-en .nav-item:nth-child(-n+7),
        body.lang-fr .nav-item:nth-child(n+8):nth-child(-n+14),
        body.lang-ar .nav-item:nth-child(n+15) {
            display: block;
        }
    }

    .nav-links a {
        display: block;
        padding: 10px 0;
    }

    .mobile-menu-btn {
        display: block;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .main-image {
        height: 350px;
    }

    .contact-container {
        flex-direction: column;
    }

    .feature-item {
        flex-direction: column;
        min-width: 250px;
    }

    .feature-icon {
        width: 100%;
        padding: 15px;
    }

    .review-item {
        min-width: 280px;
    }

    .review-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

@media (max-width: 576px) {
    .property-details {
        flex-direction: column;
        gap: 15px;
    }

    .detail-item {
        width: 100%;
    }

    .hero h1 {
        font-size: 1.8rem;
    }

    .hero p {
        font-size: 1rem;
    }

    #scroll-to-top {
        width: 40px;
        height: 40px;
        bottom: 20px;
        right: 20px;
        font-size: 1rem;
    }
}
