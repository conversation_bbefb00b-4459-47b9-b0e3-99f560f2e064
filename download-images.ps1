# PowerShell script to download placeholder images for the villa website

# Create images directory if it doesn't exist
if (-not (Test-Path -Path ".\images")) {
    New-Item -ItemType Directory -Path ".\images"
}

# URLs for placeholder images (using Unsplash for high-quality free images)
$imageUrls = @(
    "https://images.unsplash.com/photo-1580587771525-78b9dba3b914?w=1200&q=80", # Hero image
    "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=800&q=80",  # Villa 1
    "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?w=800&q=80",  # Villa 2
    "https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?w=800&q=80",  # Villa 3
    "https://images.unsplash.com/photo-1600210492493-0946911123ea?w=800&q=80",  # Villa 4
    "https://images.unsplash.com/photo-1600585154526-990dced4db0d?w=800&q=80"   # Villa 5
)

# File names for the downloaded images
$fileNames = @(
    "hero.jpg",
    "villa1.jpg",
    "villa2.jpg",
    "villa3.jpg",
    "villa4.jpg",
    "villa5.jpg"
)

# Download each image
for ($i = 0; $i -lt $imageUrls.Count; $i++) {
    $url = $imageUrls[$i]
    $fileName = $fileNames[$i]
    $outputPath = ".\images\$fileName"

    Write-Host "Downloading $fileName..."
    try {
        Invoke-WebRequest -Uri $url -OutFile $outputPath
        Write-Host "Downloaded $fileName successfully." -ForegroundColor Green
    } catch {
        Write-Host "Failed to download $fileName. Error: $_" -ForegroundColor Red
    }
}

Write-Host "`nAll images have been downloaded. You can now open index.html to view the website."
