<?php
// Email configuration
$to_email = "<EMAIL>";
$subject_prefix = "Dar Alhuriya - Contact Form Message";

// Set content type for JSON response
header('Content-Type: application/json');

// Enable CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Validate and sanitize input data
$name = isset($_POST['name']) ? trim($_POST['name']) : '';
$email = isset($_POST['email']) ? trim($_POST['email']) : '';
$phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';
$message = isset($_POST['message']) ? trim($_POST['message']) : '';

// Basic validation
if (empty($name) || empty($email) || empty($message)) {
    echo json_encode(['success' => false, 'message' => 'Required fields are missing']);
    exit;
}

// Validate email format
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    echo json_encode(['success' => false, 'message' => 'Invalid email format']);
    exit;
}

// Sanitize data
$name = htmlspecialchars($name, ENT_QUOTES, 'UTF-8');
$email = htmlspecialchars($email, ENT_QUOTES, 'UTF-8');
$phone = htmlspecialchars($phone, ENT_QUOTES, 'UTF-8');
$message = htmlspecialchars($message, ENT_QUOTES, 'UTF-8');

// Create email subject
$subject = $subject_prefix . " - From: " . $name;

// Create email body
$email_body = "New contact form submission from Dar Alhuriya website:\n\n";
$email_body .= "Name: " . $name . "\n";
$email_body .= "Email: " . $email . "\n";
$email_body .= "Phone: " . ($phone ? $phone : 'Not provided') . "\n";
$email_body .= "Message:\n" . $message . "\n\n";
$email_body .= "---\n";
$email_body .= "This message was sent from the Dar Alhuriya contact form.\n";
$email_body .= "Date: " . date('Y-m-d H:i:s') . "\n";
$email_body .= "IP Address: " . $_SERVER['REMOTE_ADDR'] . "\n";

// Email headers - try different From addresses to avoid spam filters
$headers = array();
$headers[] = "From: contact@" . $_SERVER['HTTP_HOST']; // Use your domain
$headers[] = "Reply-To: " . $email;
$headers[] = "X-Mailer: PHP/" . phpversion();
$headers[] = "Content-Type: text/plain; charset=UTF-8";
$headers[] = "X-Priority: 1";
$headers[] = "X-MSMail-Priority: High";

// Convert headers array to string
$headers_string = implode("\r\n", $headers);

// Log the attempt for debugging
error_log("Attempting to send email to: " . $to_email);
error_log("Subject: " . $subject);
error_log("From header: contact@" . $_SERVER['HTTP_HOST']);

// Send email
$mail_sent = mail($to_email, $subject, $email_body, $headers_string);

if ($mail_sent) {
    error_log("Email sent successfully to: " . $to_email);
    echo json_encode(['success' => true, 'message' => 'Email sent successfully']);
} else {
    error_log("Failed to send email to: " . $to_email);
    echo json_encode(['success' => false, 'message' => 'Failed to send email. Please check server configuration.']);
}
?>
