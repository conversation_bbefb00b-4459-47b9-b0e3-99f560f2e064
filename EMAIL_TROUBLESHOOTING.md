# Email Troubleshooting Guide - Dar <PERSON> Contact Form

## Problem: Not Receiving Emails from Contact Form

### Quick Diagnosis Steps

1. **Test Your Server Email Function**
   - Upload `test-email.php` to your server
   - Visit `https://yourwebsite.com/test-email.php` in your browser
   - This will test multiple email configurations

2. **Check Your Email**
   - Check your inbox: <EMAIL>
   - **IMPORTANT**: Check your spam/junk folder
   - Wait 5-10 minutes for delivery

3. **Test the Contact Form**
   - Go to your website
   - Fill out the contact form with test data
   - Submit and note any error messages

## Common Issues and Solutions

### Issue 1: PHP mail() Function Not Working

**Symptoms:**
- `test-email.php` shows "FAILED" for all attempts
- Contact form shows "Failed to send email"

**Solutions:**
1. **Contact Your Hosting Provider**
   - Ask if PHP mail() function is enabled
   - Ask if SMTP authentication is required
   - Request email server configuration details

2. **Switch to EmailJS (Recommended)**
   - Open `emailjs-setup.html` in your browser
   - Follow the step-by-step setup guide
   - This bypasses server email issues entirely

### Issue 2: Emails Going to Spam

**Symptoms:**
- `test-email.php` shows "SUCCESS" 
- No emails in inbox, but found in spam folder

**Solutions:**
1. **Add to Safe Senders**
   - Add the sender addresses to your Gmail safe senders list
   - Mark test emails as "Not Spam"

2. **Improve Email Headers**
   - The updated `send-email.php` includes better headers
   - Uses your domain name in the From address

### Issue 3: Server Configuration Issues

**Symptoms:**
- Inconsistent email delivery
- Some emails arrive, others don't

**Solutions:**
1. **Check Server Logs**
   - Look for email-related errors in your hosting control panel
   - Check PHP error logs

2. **Verify Domain Settings**
   - Ensure your domain has proper MX records
   - Add SPF record: `v=spf1 include:_spf.google.com ~all`

### Issue 4: Contact Form Not Submitting

**Symptoms:**
- Form submission doesn't show success/error message
- No network requests in browser developer tools

**Solutions:**
1. **Check File Paths**
   - Ensure `send-email.php` is in the same directory as `index.html`
   - Verify file permissions (644 for PHP files)

2. **Check Browser Console**
   - Open browser developer tools (F12)
   - Look for JavaScript errors in the Console tab

## Recommended Solution: EmailJS

Since server email configuration can be complex and unreliable, we recommend using EmailJS:

### Why EmailJS?
- ✅ No server configuration required
- ✅ Works with any hosting provider
- ✅ Reliable delivery (99.9% uptime)
- ✅ Real-time delivery tracking
- ✅ Free tier: 200 emails/month
- ✅ No spam folder issues

### Setup EmailJS
1. Open `emailjs-setup.html` in your browser
2. Follow the step-by-step guide
3. Takes about 10 minutes to set up
4. Test immediately after setup

## Testing Checklist

After implementing any solution, test with this checklist:

- [ ] Fill out contact form with test data
- [ ] Submit form and verify success message
- [ ] Check email inbox within 5 minutes
- [ ] Check spam/junk folder
- [ ] Test with different email addresses
- [ ] Test from different devices/browsers

## Emergency Contact Methods

If email still doesn't work, ensure these alternatives are working:

1. **WhatsApp**: https://wa.me/************
2. **Phone**: +213 558 980 843
3. **Facebook**: https://www.facebook.com/dar.alhuriya

## Files to Check

Make sure these files are uploaded to your server:

- `index.html` - Main website
- `send-email.php` - Email handler (if using PHP)
- `js/main.js` - Contact form JavaScript
- `js/emailjs-config.js` - EmailJS configuration (if using EmailJS)
- `test-email.php` - Email testing script

## Getting Help

If you're still having issues:

1. **Run the test script**: Visit `test-email.php` on your website
2. **Check browser console**: Look for JavaScript errors
3. **Contact your hosting provider**: Ask about email configuration
4. **Use EmailJS**: Follow the setup guide in `emailjs-setup.html`

## Security Notes

- Delete `test-email.php` after testing
- Keep your EmailJS credentials secure
- Regularly monitor for spam submissions
- Consider adding CAPTCHA for additional protection

---

**Last Updated**: January 2025
**Contact**: <EMAIL>
