document.addEventListener('DOMContentLoaded', function() {
    // Set default language
    setLanguage('en');

    // Scroll to Top Button Functionality
    const scrollToTopBtn = document.getElementById('scroll-to-top');

    // Show button when user scrolls down 300px
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            scrollToTopBtn.classList.add('visible');
        } else {
            scrollToTopBtn.classList.remove('visible');
        }
    });

    // Scroll to top when button is clicked
    scrollToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // Language Selector
    const langButtons = document.querySelectorAll('.lang-btn');
    langButtons.forEach(button => {
        button.addEventListener('click', function() {
            const lang = this.getAttribute('data-lang');
            setLanguage(lang);
        });
    });

    // Mobile Menu Toggle
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navLinks = document.querySelector('.nav-links');

    if (mobileMenuBtn) {
        mobileMenuBtn.addEventListener('click', function(e) {
            e.stopPropagation(); // Prevent event from bubbling to document
            navLinks.classList.toggle('active');
        });

        // Close menu when clicking on a nav link
        const navItems = document.querySelectorAll('.nav-links a');
        navItems.forEach(item => {
            item.addEventListener('click', function() {
                navLinks.classList.remove('active');
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('active') &&
                !navLinks.contains(e.target) &&
                e.target !== mobileMenuBtn) {
                navLinks.classList.remove('active');
            }
        });
    }

    // Gallery Functionality
    const thumbnails = document.querySelectorAll('.thumbnails img');
    const featuredImage = document.getElementById('featured');
    const prevBtn = document.getElementById('prev');
    const nextBtn = document.getElementById('next');
    const captionEn = document.getElementById('caption-en');
    const captionFr = document.getElementById('caption-fr');
    const captionAr = document.getElementById('caption-ar');

    // Image captions for each thumbnail - updated to match actual images
    const imageCaptions = [
        { en: "Elegant Villa Exterior with Garden", fr: "Extérieur Élégant de la Villa avec Jardin", ar: "الواجهة الخارجية الأنيقة للفيلا مع الحديقة" },
        { en: "Beautiful Villa with Landscaping", fr: "Belle Villa avec Aménagement Paysager", ar: "فيلا جميلة مع تنسيق الحدائق" },
        { en: "Front View with Elegant Entrance", fr: "Vue de Face avec Entrée Élégante", ar: "منظر أمامي مع مدخل أنيق" },
        { en: " Amazing night view ", fr: " Vue nocturne incroyable ", ar: "منظر ليلي رائع" },
        { en: " Great sea view ", fr: " Superbe vue sur la mer ", ar: " منظر رائع للبحر " },
        { en: " modern interior design ", fr: " design d'intérieur moderne ", ar: " تصميم داخلي عصري " },
        { en: "modern washing machine", fr: "machine à laver moderne", ar: "غسالة عصرية" },
        { en: " fresh air ", fr: " air frais ", ar: "هواء نقي" },
        { en: "Panoramic View from the Villa", fr: "Vue Panoramique depuis la Villa", ar: "إطلالة بانورامية من الفيلا" },
        { en: " modern interior design ", fr: " design d'intérieur moderne ", ar: " تصميم داخلي عصري " },
        { en: "clean pool", fr: "piscine propre", ar: "مسبح نقي" },
        { en: "Stunning Villa with Modern Design", fr: "Villa Impressionnante au Design Moderne", ar: "فيلا مذهلة بتصميم عصري" }
    ];

    let currentIndex = 0;

    if (thumbnails.length > 0 && featuredImage) {
        // Set initial active thumbnail
        thumbnails[0].classList.add('active');

        // Initialize caption on page load
        updateCaption();

        // Thumbnail click event
        thumbnails.forEach((thumbnail, index) => {
            thumbnail.addEventListener('click', function() {
                featuredImage.src = this.src;
                currentIndex = index;
                updateActiveThumbnail();
                updateCaption();
            });
        });

        // Previous button click
        if (prevBtn) {
            prevBtn.addEventListener('click', function() {
                currentIndex = (currentIndex - 1 + thumbnails.length) % thumbnails.length;
                featuredImage.src = thumbnails[currentIndex].src;
                updateActiveThumbnail();
                updateCaption();
            });
        }

        // Next button click
        if (nextBtn) {
            nextBtn.addEventListener('click', function() {
                currentIndex = (currentIndex + 1) % thumbnails.length;
                featuredImage.src = thumbnails[currentIndex].src;
                updateActiveThumbnail();
                updateCaption();
            });
        }
    }

    // Update active thumbnail
    function updateActiveThumbnail() {
        thumbnails.forEach(thumb => thumb.classList.remove('active'));
        thumbnails[currentIndex].classList.add('active');
    }

    // Update image caption
    function updateCaption() {
        if (imageCaptions[currentIndex]) {
            captionEn.textContent = imageCaptions[currentIndex].en;
            captionFr.textContent = imageCaptions[currentIndex].fr;
            captionAr.textContent = imageCaptions[currentIndex].ar;
        }
    }

    // Contact Form Validation and Submission
    const contactForm = document.getElementById('propertyForm');

    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const phone = document.getElementById('phone').value;
            const message = document.getElementById('message').value;

            if (!name || !email || !message) {
                alert('Please fill in all required fields');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                alert('Please enter a valid email address');
                return;
            }

            // Show loading state
            const submitBtn = contactForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Sending...';
            submitBtn.disabled = true;

            // Prepare form data
            const formData = new FormData();
            formData.append('name', name);
            formData.append('email', email);
            formData.append('phone', phone);
            formData.append('message', message);

            // Send form data to PHP script
            fetch('send-email.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Thank you for your message! We will contact you soon.');
                    contactForm.reset();
                } else {
                    alert('Sorry, there was an error sending your message. Please try again or contact us directly.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Sorry, there was an error sending your message. Please try again or contact us directly.');
            })
            .finally(() => {
                // Reset button state
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        });
    }
});

// Set language function
function setLanguage(lang) {
    // Save the selected language to localStorage
    localStorage.setItem('selectedLanguage', lang);

    // Clear existing classes and add the language class
    document.body.className = '';
    document.body.classList.add('lang-' + lang);

    // Update active language button
    const langButtons = document.querySelectorAll('.lang-btn');
    langButtons.forEach(button => {
        button.classList.remove('active');
        if (button.getAttribute('data-lang') === lang) {
            button.classList.add('active');
        }
    });

    // Set RTL for Arabic
    if (lang === 'ar') {
        document.body.classList.add('rtl');
    }

    // Update page title based on language
    updatePageTitle(lang);

    // Update active menu item
    updateActiveMenuItem();
}

// Update page title based on language
function updatePageTitle(lang) {
    const titles = {
        'en': 'Dar Alhuriya - Vacation Home in Ténès, Algeria',
        'fr': 'Dar Alhuriya - Maison de Vacances à Ténès, Algérie',
        'ar': 'دار الحرية - بيت العطلات في تنس، الجزائر'
    };

    document.title = titles[lang] || titles['en'];
}

// Update active menu item based on current section
function updateActiveMenuItem() {
    // Get all sections
    const sections = document.querySelectorAll('section');

    // Get current language
    const currentLang = document.body.className.includes('lang-en') ? 'en' :
                        document.body.className.includes('lang-fr') ? 'fr' : 'ar';

    // Get all nav links for current language
    const navLinks = document.querySelectorAll(`.nav-item a.${currentLang}`);

    // Remove active class from all links
    navLinks.forEach(link => link.classList.remove('active'));

    // Determine which section is currently in view
    let currentSection = '';
    let minDistance = Infinity;

    sections.forEach(section => {
        const rect = section.getBoundingClientRect();
        const distance = Math.abs(rect.top);

        if (distance < minDistance) {
            minDistance = distance;
            currentSection = section.id;
        }
    });

    // Add active class to the link for the current section
    if (currentSection) {
        const activeLink = document.querySelector(`.nav-item a.${currentLang}[href="#${currentSection}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
    }
}

// Load saved language preference on page load
document.addEventListener('DOMContentLoaded', function() {
    const savedLanguage = localStorage.getItem('selectedLanguage');
    if (savedLanguage) {
        setLanguage(savedLanguage);
    } else {
        // Default to browser language or English
        const browserLang = navigator.language.split('-')[0];
        if (browserLang === 'fr' || browserLang === 'ar') {
            setLanguage(browserLang);
        } else {
            setLanguage('en');
        }
    }

    // Update active menu item on scroll
    window.addEventListener('scroll', function() {
        updateActiveMenuItem();
    });
});

// Google Maps is now embedded via iframe in the HTML
