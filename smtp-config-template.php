<?php
// SMTP Configuration Template
// Use this if your hosting provider requires SMTP authentication

// SMTP Settings - GET THESE FROM YOUR HOSTING PROVIDER
$smtp_host = 'mail.yourdomain.com';     // SMTP server address
$smtp_port = 587;                       // SMTP port (587 for TLS, 465 for SSL)
$smtp_username = '<EMAIL>'; // SMTP username
$smtp_password = 'your_password';       // SMTP password
$smtp_encryption = 'tls';               // 'tls' or 'ssl'

// Email configuration
$to_email = "<EMAIL>";
$from_email = $smtp_username;           // Must match SMTP username
$from_name = "Dar Alhuriya Contact Form";

// This is a template - you'll need PHPMailer library for actual SMTP sending
// Download from: https://github.com/PHPMailer/PHPMailer

/*
Example SMTP configuration for common providers:

GMAIL SMTP:
- Host: smtp.gmail.com
- Port: 587
- Encryption: TLS
- Username: <EMAIL>
- Password: your-app-password (not regular password!)

OUTLOOK/HOTMAIL SMTP:
- Host: smtp-mail.outlook.com
- Port: 587
- Encryption: TLS
- Username: <EMAIL>
- Password: your-password

HOSTING PROVIDER SMTP (varies):
- Host: mail.yourdomain.com (or smtp.yourdomain.com)
- Port: 587 or 465
- Username: <EMAIL>
- Password: provided by host
*/

echo "<h1>SMTP Configuration Template</h1>";
echo "<p>This file shows what SMTP settings you need from your hosting provider.</p>";

echo "<h2>Questions to Ask Your Hosting Provider:</h2>";
echo "<ol>";
echo "<li><strong>SMTP Server Address:</strong> What is the SMTP hostname? (e.g., mail.yourdomain.com)</li>";
echo "<li><strong>SMTP Port:</strong> What port should I use? (usually 587 or 465)</li>";
echo "<li><strong>Encryption:</strong> Should I use TLS or SSL?</li>";
echo "<li><strong>Username:</strong> What username should I use? (usually an email address)</li>";
echo "<li><strong>Password:</strong> What is the password for SMTP authentication?</li>";
echo "<li><strong>Authentication Required:</strong> Do I need to authenticate?</li>";
echo "</ol>";

echo "<h2>Alternative: Use Gmail SMTP</h2>";
echo "<p>If your hosting provider doesn't offer SMTP, you can use Gmail:</p>";
echo "<ol>";
echo "<li>Enable 2-factor authentication on your Gmail account</li>";
echo "<li>Generate an 'App Password' for your website</li>";
echo "<li>Use these settings:</li>";
echo "<ul>";
echo "<li>Host: smtp.gmail.com</li>";
echo "<li>Port: 587</li>";
echo "<li>Encryption: TLS</li>";
echo "<li>Username: <EMAIL></li>";
echo "<li>Password: [your app password]</li>";
echo "</ul>";
echo "</ol>";

echo "<h2>Next Steps:</h2>";
echo "<ol>";
echo "<li>Contact your hosting provider with the questions above</li>";
echo "<li>Get the SMTP credentials</li>";
echo "<li>Let me know the details, and I'll configure your contact form</li>";
echo "</ol>";

echo "<hr>";
echo "<p><em>Delete this file after getting your SMTP details.</em></p>";
?>
