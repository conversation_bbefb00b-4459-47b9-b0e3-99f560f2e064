<?php
// Enhanced test script to verify email functionality
// Upload this file to your server and visit it in your browser to test email sending

$to_email = "<EMAIL>";
$subject = "Test Email from Dar Alhuriya Website - " . date('Y-m-d H:i:s');
$message = "This is a test email to verify that the email functionality is working correctly.\n\n";
$message .= "If you receive this email, your server can send emails successfully.\n";
$message .= "Date: " . date('Y-m-d H:i:s') . "\n";
$message .= "Server: " . $_SERVER['HTTP_HOST'] . "\n";
$message .= "Server IP: " . $_SERVER['SERVER_ADDR'] . "\n";
$message .= "PHP Version: " . phpversion() . "\n";

// Try multiple From addresses to see which works
$from_addresses = [
    "contact@" . $_SERVER['HTTP_HOST'],
    "noreply@" . $_SERVER['HTTP_HOST'],
    "test@" . $_SERVER['HTTP_HOST'],
    "<EMAIL>"
];

echo "<h1>Email Test for Dar Alhuriya</h1>";
echo "<p>Testing multiple email configurations...</p>";

foreach ($from_addresses as $from_address) {
    echo "<h3>Testing with From: $from_address</h3>";

    $headers = array();
    $headers[] = "From: $from_address";
    $headers[] = "X-Mailer: PHP/" . phpversion();
    $headers[] = "Content-Type: text/plain; charset=UTF-8";
    $headers[] = "X-Priority: 1";

    $headers_string = implode("\r\n", $headers);

    $test_message = $message . "\nFrom address used: $from_address\n";
    $test_subject = $subject . " (From: $from_address)";

    $mail_sent = mail($to_email, $test_subject, $test_message, $headers_string);

    if ($mail_sent) {
        echo "<p style='color: green;'><strong>SUCCESS!</strong> Test email sent with $from_address</p>";
    } else {
        echo "<p style='color: red;'><strong>FAILED!</strong> Could not send with $from_address</p>";
    }

    // Small delay between attempts
    sleep(1);
}

echo "<hr>";
echo "<h2>Server Information</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Server:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";
echo "<p><strong>Server IP:</strong> " . ($_SERVER['SERVER_ADDR'] ?? 'Unknown') . "</p>";
echo "<p><strong>Mail Function Available:</strong> " . (function_exists('mail') ? 'Yes' : 'No') . "</p>";

if (function_exists('mail')) {
    echo "<p style='color: green;'>✓ PHP mail() function is available</p>";
} else {
    echo "<p style='color: red;'>✗ PHP mail() function is NOT available</p>";
}

echo "<hr>";
echo "<h2>Troubleshooting Tips</h2>";
echo "<ul>";
echo "<li>Check your spam/junk folder</li>";
echo "<li>Wait a few minutes for email delivery</li>";
echo "<li>If no emails arrive, contact your hosting provider</li>";
echo "<li>Some hosts require SMTP authentication instead of mail()</li>";
echo "</ul>";

echo "<hr>";
echo "<p><em>Delete this file after testing for security reasons.</em></p>";
?>
