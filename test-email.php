<?php
// Simple test script to verify email functionality
// Upload this file to your server and visit it in your browser to test email sending

$to_email = "<EMAIL>";
$subject = "Test Email from Dar Alhuriya Website";
$message = "This is a test email to verify that the email functionality is working correctly.\n\n";
$message .= "If you receive this email, your server can send emails successfully.\n";
$message .= "Date: " . date('Y-m-d H:i:s') . "\n";
$message .= "Server: " . $_SERVER['HTTP_HOST'] . "\n";

$headers = array();
$headers[] = "From: <EMAIL>";
$headers[] = "X-Mailer: PHP/" . phpversion();
$headers[] = "Content-Type: text/plain; charset=UTF-8";

$headers_string = implode("\r\n", $headers);

echo "<h1>Email Test for Dar Alhuriya</h1>";
echo "<p>Testing email functionality...</p>";

$mail_sent = mail($to_email, $subject, $message, $headers_string);

if ($mail_sent) {
    echo "<p style='color: green;'><strong>SUCCESS!</strong> Test email sent to " . $to_email . "</p>";
    echo "<p>Check your inbox (and spam folder) for the test email.</p>";
} else {
    echo "<p style='color: red;'><strong>FAILED!</strong> Could not send test email.</p>";
    echo "<p>Possible issues:</p>";
    echo "<ul>";
    echo "<li>PHP mail() function is not enabled on this server</li>";
    echo "<li>Server is not configured to send emails</li>";
    echo "<li>The 'From' email address needs to be changed</li>";
    echo "<li>SMTP configuration is required</li>";
    echo "</ul>";
    echo "<p>Contact your hosting provider for assistance.</p>";
}

echo "<hr>";
echo "<h2>Server Information</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Server:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";
echo "<p><strong>Mail Function Available:</strong> " . (function_exists('mail') ? 'Yes' : 'No') . "</p>";

if (function_exists('mail')) {
    echo "<p style='color: green;'>✓ PHP mail() function is available</p>";
} else {
    echo "<p style='color: red;'>✗ PHP mail() function is NOT available</p>";
}

echo "<hr>";
echo "<p><em>Delete this file after testing for security reasons.</em></p>";
?>
