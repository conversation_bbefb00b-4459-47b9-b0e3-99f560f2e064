# Email Setup Instructions for Dar Alhuriya Website

This document explains how to configure email sending to `<EMAIL>` for your contact form.

## What Has Been Changed

### Files Modified:
1. **js/main.js** - Updated contact form to send data to backend
2. **send-email.php** - NEW: PHP script to handle email sending
3. **js/emailjs-config.js** - NEW: Alternative EmailJS configuration
4. **EMAIL_SETUP_INSTRUCTIONS.md** - NEW: This instruction file

## Option 1: PHP Backend Solution (Recommended)

### Requirements:
- Web hosting with PHP support
- PHP mail() function enabled on your server

### Setup Steps:

1. **Upload Files**: Upload `send-email.php` to your web server in the same directory as `index.html`

2. **Test PHP Mail**: Create a test file to verify PHP mail works on your server:
   ```php
   <?php
   $test = mail('<EMAIL>', 'Test Email', 'This is a test email from your server.');
   echo $test ? 'Mail function works!' : 'Mail function not working';
   ?>
   ```

3. **Configure Email Headers**: If needed, modify the `send-email.php` file:
   - Change the "From" email address if your hosting provider requires it
   - Add SMTP configuration if your host requires it

### Current Configuration:
- **Recipient**: <EMAIL>
- **Subject**: "Dar Alhuriya - Contact Form Message - From: [Name]"
- **From**: <EMAIL> (you may need to change this)

## Option 2: EmailJS Solution (No Server Required)

### Advantages:
- No server-side code needed
- Works with static hosting (GitHub Pages, Netlify, etc.)
- Free tier available

### Setup Steps:

1. **Create EmailJS Account**:
   - Go to https://www.emailjs.com/
   - Sign up for a free account

2. **Create Email Service**:
   - Add an email service (Gmail, Outlook, etc.)
   - Follow EmailJS instructions to connect your email

3. **Create Email Template**:
   - Create a new template with these variables:
     ```
     To: {{to_email}}
     From: {{from_name}} <{{from_email}}>
     Subject: Dar Alhuriya Contact Form - {{from_name}}
     
     Name: {{from_name}}
     Email: {{from_email}}
     Phone: {{phone}}
     
     Message:
     {{message}}
     ```

4. **Get Your Credentials**:
   - Service ID
   - Template ID
   - Public Key

5. **Update Configuration**:
   - Edit `js/emailjs-config.js`
   - Replace `YOUR_SERVICE_ID`, `YOUR_TEMPLATE_ID`, and `YOUR_PUBLIC_KEY`

6. **Enable EmailJS**:
   - Add this line to your `index.html` before closing `</body>`:
     ```html
     <script src="js/emailjs-config.js"></script>
     ```
   - In `js/emailjs-config.js`, uncomment the line:
     ```javascript
     setupEmailJSContactForm();
     ```

## Option 3: SMTP Configuration (Advanced)

If your hosting provider requires SMTP authentication, you can modify the PHP script to use PHPMailer:

1. **Download PHPMailer**: https://github.com/PHPMailer/PHPMailer
2. **Configure SMTP settings** in a new PHP file
3. **Update send-email.php** to use PHPMailer instead of mail()

## Testing Your Setup

### For PHP Solution:
1. Upload all files to your web server
2. Open your website
3. Fill out the contact form
4. Check if email <NAME_EMAIL>
5. Check server error logs if emails don't arrive

### For EmailJS Solution:
1. Complete EmailJS setup
2. Update the configuration file
3. Test the form on your website
4. Check EmailJS dashboard for delivery status

## Troubleshooting

### Common Issues:

1. **PHP mail() not working**:
   - Contact your hosting provider
   - Check if mail() function is enabled
   - Verify server can send emails

2. **Emails going to spam**:
   - Add SPF/DKIM records to your domain
   - Use a proper "From" email address
   - Avoid spam trigger words

3. **CORS errors**:
   - Make sure files are served from the same domain
   - Check server CORS configuration

4. **EmailJS not working**:
   - Verify all credentials are correct
   - Check EmailJS dashboard for errors
   - Ensure template variables match

## Security Considerations

1. **Rate Limiting**: Consider adding rate limiting to prevent spam
2. **Validation**: Server-side validation is already included
3. **Sanitization**: Input sanitization is implemented
4. **CAPTCHA**: Consider adding CAPTCHA for additional protection

## Current Email Template

When someone submits the form, the email sent to `<EMAIL>` will look like this:

```
Subject: Dar Alhuriya - Contact Form Message - From: [Customer Name]

New contact form submission from Dar Alhuriya website:

Name: [Customer Name]
Email: [Customer Email]
Phone: [Customer Phone or "Not provided"]
Message:
[Customer Message]

---
This message was sent from the Dar Alhuriya contact form.
Date: 2025-01-27 14:30:15
IP Address: [Customer IP]
```

## Next Steps

1. Choose either PHP or EmailJS solution
2. Follow the setup instructions for your chosen option
3. Test the contact form thoroughly
4. Monitor email delivery to ensure it's working correctly

For any issues, check your server error logs or EmailJS dashboard for detailed error messages.
