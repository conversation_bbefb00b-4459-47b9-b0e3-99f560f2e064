# Villa Algérie - Real Estate Website

A multilingual (English, French, Arabic) real estate website for showcasing a luxury villa in Algeria.

## Features

- Responsive design that works on all devices
- Multilingual support (English, French, Arabic)
- Photo gallery with slider functionality
- Google Maps integration
- Contact form with validation
- Property details and description


## Setup Instructions

1. Clone this repository to your local machine
2. Add your own villa images to the `images` folder:
   - Add a main hero image named `hero.jpg`
   - Add gallery images named `villa1.jpg`, `villa2.jpg`, etc.
3. Replace the Google Maps API key in the `index.html` file:
   ```html
   <script src="https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initMap" async defer></script>
   ```
4. Customize the property details and description in the `index.html` file
5. Open `index.html` in your web browser to view the website

## Folder Structure

```
villa2/
├── css/
│   └── styles.css
├── images/
│   ├── [various villa images]
│   └── ...
├── js/
│   └── main.js
├── index.html
└── README.md
```

## Customization

### Adding More Images

1. Add your image files to the `images` folder
2. Update the HTML in the gallery section of `index.html`:
   ```html
   <div class="thumbnails">
       <img src="images/villa1.jpg" alt="Villa" class="active">
       <img src="images/villa2.jpg" alt="Villa">
       <!-- Add more images here -->
   </div>
   ```

### Changing Property Details

Update the property details in the description section of `index.html`:
```html
<div class="property-details">
    <div class="detail-item">
        <i class="fas fa-ruler-combined"></i>
        <span>350 m²</span>
    </div>
    <!-- Update other details as needed -->
</div>
```

### Updating Contact Information

Modify the contact information in the contact section of `index.html`:
```html
<div class="contact-info">
    <div class="info-item">
        <i class="fas fa-map-marker-alt"></i>
        <p>Algiers, Algeria</p>
    </div>
    <!-- Update other contact details as needed -->
</div>
```

## Technologies Used

- HTML5
- CSS3
- JavaScript (ES6)

- Google Maps API
- Font Awesome Icons
- Google Fonts



## License

This project is available for personal and commercial use.
