// EmailJS Configuration for Dar Alhuriya Contact Form
// This is an alternative to the PHP solution that works entirely client-side

// EmailJS Configuration
const EMAILJS_CONFIG = {
    // You need to replace these with your actual EmailJS credentials
    SERVICE_ID: 'YOUR_SERVICE_ID',        // Replace with your EmailJS service ID
    TEMPLATE_ID: 'YOUR_TEMPLATE_ID',      // Replace with your EmailJS template ID
    PUBLIC_KEY: 'YOUR_PUBLIC_KEY'        // Replace with your EmailJS public key
};

// Initialize EmailJS
function initEmailJS() {
    // Load EmailJS library if not already loaded
    if (typeof emailjs === 'undefined') {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js';
        script.onload = function() {
            emailjs.init(EMAILJS_CONFIG.PUBLIC_KEY);
        };
        document.head.appendChild(script);
    } else {
        emailjs.init(EMAILJS_CONFIG.PUBLIC_KEY);
    }
}

// Send email using EmailJS
function sendEmailViaEmailJS(formData) {
    return new Promise((resolve, reject) => {
        // Prepare template parameters
        const templateParams = {
            to_email: '<EMAIL>',
            from_name: formData.get('name'),
            from_email: formData.get('email'),
            phone: formData.get('phone') || 'Not provided',
            message: formData.get('message'),
            reply_to: formData.get('email')
        };

        // Send email
        emailjs.send(
            EMAILJS_CONFIG.SERVICE_ID,
            EMAILJS_CONFIG.TEMPLATE_ID,
            templateParams
        ).then(
            function(response) {
                console.log('Email sent successfully:', response);
                resolve({ success: true, message: 'Email sent successfully' });
            },
            function(error) {
                console.error('Email sending failed:', error);
                reject({ success: false, message: 'Failed to send email' });
            }
        );
    });
}

// Alternative contact form handler using EmailJS
function setupEmailJSContactForm() {
    const contactForm = document.getElementById('propertyForm');

    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const phone = document.getElementById('phone').value;
            const message = document.getElementById('message').value;

            if (!name || !email || !message) {
                alert('Please fill in all required fields');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                alert('Please enter a valid email address');
                return;
            }

            // Show loading state
            const submitBtn = contactForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Sending...';
            submitBtn.disabled = true;

            // Prepare form data
            const formData = new FormData();
            formData.append('name', name);
            formData.append('email', email);
            formData.append('phone', phone);
            formData.append('message', message);

            // Send email using EmailJS
            sendEmailViaEmailJS(formData)
                .then(data => {
                    alert('Thank you for your message! We will contact you soon.');
                    contactForm.reset();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Sorry, there was an error sending your message. Please try again or contact us directly.');
                })
                .finally(() => {
                    // Reset button state
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                });
        });
    }
}

// Initialize EmailJS when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initEmailJS();
    // Uncomment the line below if you want to use EmailJS instead of PHP
    // setupEmailJSContactForm();
});
